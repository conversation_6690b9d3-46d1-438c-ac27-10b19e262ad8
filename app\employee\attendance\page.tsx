"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/components/auth-provider"
import { Clock, CheckCircle, XCircle, Loader2, AlertCircle, Timer } from "lucide-react"
import { AppHeader } from "@/components/app-header"
import { toast } from "@/hooks/use-toast"
import {
  formatTime,
  formatDate,
  getCurrentWorkDuration,
  getAttendanceStatsSummary,
  getTodayDate
} from "@/lib/attendance-utils"
import {
  withRetry,
  getUserFriendlyError,
  logError,
  ATTENDANCE_ERRORS
} from "@/lib/error-handling"

interface AttendanceRecord {
  id: string
  user_id: string
  date: string
  check_in_time?: string
  check_out_time?: string
  status: "present" | "absent" | "late" | "half_day" | "on_leave"
  hours_worked?: number
  notes?: string
  created_at: string
  updated_at: string
}

interface AttendanceStats {
  totalDays: number
  presentDays: number
  absentDays: number
  lateDays: number
  totalHoursWorked: number
  averageHoursPerDay: number
}

export default function AttendancePage() {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [todayAttendance, setTodayAttendance] = useState<AttendanceRecord | null>(null)
  const [isCheckedIn, setIsCheckedIn] = useState(false)
  const [attendanceHistory, setAttendanceHistory] = useState<AttendanceRecord[]>([])
  const [attendanceStats, setAttendanceStats] = useState<AttendanceStats | null>(null)
  const [notes, setNotes] = useState("")
  const [showNotesInput, setShowNotesInput] = useState(false)
  const [currentWorkTime, setCurrentWorkTime] = useState<string>("")
  const [realTimeInterval, setRealTimeInterval] = useState<NodeJS.Timeout | null>(null)

  // New state for multiple entries system
  const [todayEntries, setTodayEntries] = useState<AttendanceRecord[]>([])
  const [totalHoursToday, setTotalHoursToday] = useState<number>(0)
  const [remainingCheckIns, setRemainingCheckIns] = useState<number>(5)
  const [remainingCheckOuts, setRemainingCheckOuts] = useState<number>(5)
  const [activeSession, setActiveSession] = useState<AttendanceRecord | null>(null)
  const [warnings, setWarnings] = useState<string[]>([])
  const [dailySummary, setDailySummary] = useState<{
    completedSessions: number;
    activeSessions: number;
    formattedTotal: string;
  }>({ completedSessions: 0, activeSessions: 0, formattedTotal: '00:00:00' })

  // Fetch attendance status and history on component mount
  useEffect(() => {
    fetchAttendanceStatus()
    fetchAttendanceHistory()
  }, [])

  // Real-time work duration update for checked-in users
  useEffect(() => {
    if (isCheckedIn && activeSession?.check_in_time) {
      const interval = setInterval(() => {
        const duration = getCurrentWorkDuration(activeSession.check_in_time!)
        setCurrentWorkTime(duration.formattedDuration)
      }, 1000) // Update every second

      setRealTimeInterval(interval)
      return () => {
        if (interval) clearInterval(interval)
      }
    } else {
      if (realTimeInterval) {
        clearInterval(realTimeInterval)
        setRealTimeInterval(null)
      }
      setCurrentWorkTime("")
    }
  }, [isCheckedIn, activeSession?.check_in_time])

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (realTimeInterval) {
        clearInterval(realTimeInterval)
      }
    }
  }, [])

  const fetchAttendanceStatus = async () => {
    try {
      const response = await fetch("/api/attendance/status", {
        credentials: "include",
      })
      const data = await response.json()

      if (data.success) {
        setTodayAttendance(data.attendance)
        setIsCheckedIn(data.isCheckedIn)
        setTodayEntries(data.todayEntries || [])
        setTotalHoursToday(data.totalHoursToday || 0)
        setRemainingCheckIns(data.remainingCheckIns || 5)
        setRemainingCheckOuts(data.remainingCheckOuts || 5)
        setActiveSession(data.activeSession)
        setWarnings(data.warnings || [])
        setDailySummary(data.dailySummary || { completedSessions: 0, activeSessions: 0, formattedTotal: '00:00:00' })
      }
    } catch (error) {
      console.error("Error fetching attendance status:", error)
      toast({
        title: "Error",
        description: "Failed to fetch attendance status",
        variant: "destructive",
      })
    }
  }

  const fetchAttendanceHistory = async () => {
    try {
      const response = await fetch("/api/attendance/history", {
        credentials: "include",
      })
      const data = await response.json()

      if (data.success) {
        setAttendanceHistory(data.history)
        setAttendanceStats(data.stats)
      }
    } catch (error) {
      console.error("Error fetching attendance history:", error)
      toast({
        title: "Error",
        description: "Failed to fetch attendance history",
        variant: "destructive",
      })
    }
  }

  const handleClockIn = async () => {
    // Validate notes length
    if (notes.trim().length > 500) {
      toast({
        title: "Error",
        description: "Notes cannot exceed 500 characters",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    try {
      const clockInOperation = async () => {
        const response = await fetch("/api/attendance/clock-in", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({ notes: notes.trim() || undefined }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "Failed to clock in")
        }

        return response.json()
      }

      const data = await withRetry(clockInOperation, 2, 1000)

      if (data.success) {
        setTodayAttendance(data.data)
        setIsCheckedIn(true)
        setNotes("")
        setShowNotesInput(false)
        toast({
          title: "Success",
          description: data.message,
        })
        // Refresh history to show updated stats
        fetchAttendanceHistory()
      } else {
        throw new Error(data.error || "Failed to clock in")
      }
    } catch (error) {
      logError(error, "Employee clock in")
      const userFriendlyMessage = getUserFriendlyError(error)

      toast({
        title: "Error",
        description: userFriendlyMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleClockOut = async () => {
    // Validate notes length
    if (notes.trim().length > 500) {
      toast({
        title: "Error",
        description: "Notes cannot exceed 500 characters",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    try {
      const clockOutOperation = async () => {
        const response = await fetch("/api/attendance/clock-out", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({ notes: notes.trim() || undefined }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "Failed to clock out")
        }

        return response.json()
      }

      const data = await withRetry(clockOutOperation, 2, 1000)

      if (data.success) {
        setTodayAttendance(data.data)
        setIsCheckedIn(false)
        setNotes("")
        setShowNotesInput(false)
        toast({
          title: "Success",
          description: data.message,
        })
        // Refresh history to show updated stats
        fetchAttendanceHistory()
      } else {
        throw new Error(data.error || "Failed to clock out")
      }
    } catch (error) {
      logError(error, "Employee clock out")
      const userFriendlyMessage = getUserFriendlyError(error)

      toast({
        title: "Error",
        description: userFriendlyMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Use utility functions for consistent formatting

  const getStatusBadge = (status: string) => {
    const variants = {
      present: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      late: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
      absent: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
      half_day: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      on_leave: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
    }
    return variants[status as keyof typeof variants] || variants.present
  }

  return (
    <div className="min-h-screen bg-background">
      <AppHeader />
      <div className="container mx-auto px-4 py-6 pb-24 md:pb-6">
        <h1 className="text-2xl font-bold mb-6 text-foreground">Attendance</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="col-span-1 bg-card text-card-foreground">
            <CardHeader>
              <CardTitle className="flex items-center text-foreground">
                <Clock className="mr-2" />
                Today's Attendance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center space-y-4">
                <div className="text-center">
                  <p className="text-lg font-medium text-foreground">
                    {new Date().toLocaleDateString("en-US", {
                      weekday: "long",
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </p>
                  {todayAttendance?.check_in_time && (
                    <div className="mt-2 space-y-1">
                      <p className="text-sm text-muted-foreground">
                        Check-in: {formatTime(todayAttendance.check_in_time)}
                      </p>
                      {todayAttendance.check_out_time && (
                        <p className="text-sm text-muted-foreground">
                          Check-out: {formatTime(todayAttendance.check_out_time)}
                        </p>
                      )}
                      {todayAttendance.status && (
                        <Badge className={getStatusBadge(todayAttendance.status)}>
                          {todayAttendance.status.replace("_", " ").toUpperCase()}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>

                {showNotesInput && (
                  <div className="w-full space-y-2">
                    <Label htmlFor="notes">Notes (optional)</Label>
                    <Textarea
                      id="notes"
                      placeholder="Add any notes about your attendance..."
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      rows={3}
                      maxLength={500}
                      className={notes.length > 450 ? "border-yellow-500" : ""}
                    />
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <span>Optional notes about your attendance</span>
                      <span className={notes.length > 450 ? "text-yellow-600" : ""}>
                        {notes.length}/500
                      </span>
                    </div>
                  </div>
                )}

                <div className="flex flex-col sm:flex-row gap-4 w-full justify-center">
                  {!isCheckedIn ? (
                    <>
                      <Button
                        onClick={() => setShowNotesInput(!showNotesInput)}
                        variant="outline"
                        size="sm"
                        className="flex items-center"
                      >
                        <AlertCircle className="mr-2 h-4 w-4" />
                        {showNotesInput ? "Hide Notes" : "Add Notes"}
                      </Button>
                      <Button
                        onClick={handleClockIn}
                        disabled={loading || remainingCheckIns <= 0}
                        className="flex items-center"
                        size="lg"
                      >
                        {loading ? <Loader2 className="mr-2 h-5 w-5 animate-spin" /> : <CheckCircle className="mr-2 h-5 w-5" />}
                        {remainingCheckIns <= 0 ? "Daily Limit Reached" : "Check In"}
                      </Button>
                      {remainingCheckIns <= 0 && (
                        <p className="text-sm text-red-600 dark:text-red-400 text-center">
                          You have reached the maximum of 5 check-ins per day.
                        </p>
                      )}
                    </>
                  ) : (
                    <>
                      <Button
                        onClick={() => setShowNotesInput(!showNotesInput)}
                        variant="outline"
                        size="sm"
                        className="flex items-center"
                      >
                        <AlertCircle className="mr-2 h-4 w-4" />
                        {showNotesInput ? "Hide Notes" : "Add Notes"}
                      </Button>
                      <Button
                        onClick={handleClockOut}
                        disabled={loading || remainingCheckOuts <= 0}
                        variant="outline"
                        className="flex items-center border-border"
                        size="lg"
                      >
                        {loading ? <Loader2 className="mr-2 h-5 w-5 animate-spin" /> : <XCircle className="mr-2 h-5 w-5" />}
                        {remainingCheckOuts <= 0 ? "Daily Limit Reached" : "Check Out"}
                      </Button>
                      {remainingCheckOuts <= 0 && (
                        <p className="text-sm text-red-600 dark:text-red-400 text-center">
                          You have reached the maximum of 5 check-outs per day.
                        </p>
                      )}
                    </>
                  )}
                </div>

                {isCheckedIn && activeSession?.check_in_time && (
                  <div className="bg-green-100 dark:bg-green-900/30 p-4 rounded-md text-center w-full space-y-2">
                    <p className="text-green-700 dark:text-green-300">
                      You are currently checked in since {formatTime(activeSession.check_in_time)}
                    </p>
                    {currentWorkTime && (
                      <div className="flex items-center justify-center space-x-2">
                        <Timer className="h-4 w-4 text-green-600" />
                        <span className="font-mono text-lg font-semibold text-green-700 dark:text-green-300">
                          {currentWorkTime}
                        </span>
                      </div>
                    )}
                  </div>
                )}

                {/* Warnings */}
                {warnings.length > 0 && (
                  <div className="bg-yellow-50 dark:bg-yellow-900/30 p-4 rounded-md w-full space-y-2">
                    <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 flex items-center">
                      <AlertCircle className="mr-2 h-4 w-4" />
                      Attention
                    </h3>
                    <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                      {warnings.map((warning, index) => (
                        <li key={index} className="flex items-start">
                          <span className="mr-2">•</span>
                          <span>{warning}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Daily Summary */}
                <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-md w-full space-y-2">
                  <h3 className="font-semibold text-blue-800 dark:text-blue-200">Today's Summary</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-blue-600 dark:text-blue-300">Total Hours: <span className="font-semibold">{totalHoursToday.toFixed(1)}h</span></p>
                      <p className="text-blue-600 dark:text-blue-300">Completed: <span className="font-semibold">{dailySummary.completedSessions}</span></p>
                      <p className="text-blue-600 dark:text-blue-300">Active: <span className="font-semibold">{dailySummary.activeSessions}</span></p>
                    </div>
                    <div>
                      <p className="text-blue-600 dark:text-blue-300">Check-ins left: <span className="font-semibold">{remainingCheckIns}</span></p>
                      <p className="text-blue-600 dark:text-blue-300">Check-outs left: <span className="font-semibold">{remainingCheckOuts}</span></p>
                      <p className="text-blue-600 dark:text-blue-300">Formatted: <span className="font-mono font-semibold">{dailySummary.formattedTotal}</span></p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="col-span-1 bg-card text-card-foreground">
            <CardHeader>
              <CardTitle className="text-foreground">Attendance Summary (Last 30 Days)</CardTitle>
            </CardHeader>
            <CardContent>
              {attendanceStats ? (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-foreground">Total Days:</span>
                    <span className="font-medium text-foreground">{attendanceStats.totalDays}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-foreground">Present Days:</span>
                    <span className="font-medium text-green-600">{attendanceStats.presentDays}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-foreground">Absent Days:</span>
                    <span className="font-medium text-red-600">{attendanceStats.absentDays}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-foreground">Late Check-ins:</span>
                    <span className="font-medium text-yellow-600">{attendanceStats.lateDays}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-foreground">Total Hours Worked:</span>
                    <span className="font-medium text-foreground">{attendanceStats.totalHoursWorked.toFixed(1)}h</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-foreground">Average Hours/Day:</span>
                    <span className="font-medium text-foreground">{attendanceStats.averageHoursPerDay.toFixed(1)}h</span>
                  </div>
                  {(() => {
                    const summary = getAttendanceStatsSummary(attendanceStats)
                    return (
                      <>
                        <div className="border-t pt-2 mt-2">
                          <div className="flex justify-between">
                            <span className="text-foreground">Attendance Rate:</span>
                            <span className="font-medium text-blue-600">{summary.attendanceRate}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-foreground">Punctuality Rate:</span>
                            <span className="font-medium text-purple-600">{summary.punctualityRate}%</span>
                          </div>
                        </div>
                      </>
                    )
                  })()}
                </div>
              ) : (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Today's Entries Section */}
        {todayEntries.length > 0 && (
          <div className="mt-8">
            <h2 className="text-xl font-semibold mb-4 text-foreground">Today's Check-in/Check-out History</h2>
            <div className="overflow-x-auto bg-card rounded-lg border border-border">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-muted">
                    <th className="p-3 text-left text-foreground">#</th>
                    <th className="p-3 text-left text-foreground">Check In</th>
                    <th className="p-3 text-left text-foreground">Check Out</th>
                    <th className="p-3 text-left text-foreground">Duration</th>
                    <th className="p-3 text-left text-foreground">Status</th>
                    <th className="p-3 text-left text-foreground">Notes</th>
                  </tr>
                </thead>
                <tbody>
                  {todayEntries.map((entry, index) => (
                    <tr key={entry.id} className="border-b border-border">
                      <td className="p-3 text-foreground">{index + 1}</td>
                      <td className="p-3 text-foreground">
                        {entry.check_in_time ? formatTime(entry.check_in_time) : '-'}
                      </td>
                      <td className="p-3 text-foreground">
                        {entry.check_out_time ? formatTime(entry.check_out_time) : (
                          <span className="text-green-600 font-semibold">Active</span>
                        )}
                      </td>
                      <td className="p-3 text-foreground">
                        {entry.hours_worked ? `${Number(entry.hours_worked).toFixed(1)}h` : (
                          entry.check_in_time && !entry.check_out_time ? (
                            <span className="text-blue-600 font-mono">{currentWorkTime || 'Calculating...'}</span>
                          ) : '-'
                        )}
                      </td>
                      <td className="p-3">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          entry.status === 'present' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          entry.status === 'late' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}>
                          {entry.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </td>
                      <td className="p-3 text-foreground text-sm">
                        {entry.notes || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        <h2 className="text-xl font-semibold mt-8 mb-4 text-foreground">Attendance History</h2>
        <div className="overflow-x-auto bg-card rounded-lg border border-border">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted">
                <th className="p-3 text-left text-foreground">Date</th>
                <th className="p-3 text-left text-foreground">Status</th>
                <th className="p-3 text-left text-foreground">Check In</th>
                <th className="p-3 text-left text-foreground">Check Out</th>
                <th className="p-3 text-left text-foreground">Hours Worked</th>
                <th className="p-3 text-left text-foreground">Notes</th>
              </tr>
            </thead>
            <tbody>
              {attendanceHistory.length > 0 ? (
                attendanceHistory.map((record) => (
                  <tr key={record.id} className="border-b border-border">
                    <td className="p-3 text-foreground">{formatDate(record.date)}</td>
                    <td className="p-3">
                      <Badge className={getStatusBadge(record.status)}>
                        {record.status.replace("_", " ").toUpperCase()}
                      </Badge>
                    </td>
                    <td className="p-3 text-foreground">{formatTime(record.check_in_time)}</td>
                    <td className="p-3 text-foreground">{formatTime(record.check_out_time)}</td>
                    <td className="p-3 text-foreground">
                      {record.hours_worked ? `${Number(record.hours_worked).toFixed(1)}h` : "-"}
                    </td>
                    <td className="p-3 text-foreground text-sm">
                      {record.notes ? (
                        <span className="truncate max-w-xs block" title={record.notes}>
                          {record.notes}
                        </span>
                      ) : (
                        "-"
                      )}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="p-8 text-center text-muted-foreground">
                    No attendance records found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
