import { neon } from "@neondatabase/serverless"
import { randomBytes } from "crypto"

// Only initialize database connection on server side
let sql: any = null

if (typeof window === 'undefined') {
  // Server-side only
  if (!process.env.DATABASE_URL) {
    throw new Error("DATABASE_URL environment variable is required")
  }
  sql = neon(process.env.DATABASE_URL)
}

// Types for our database
export type UserRole = "admin" | "hr_manager" | "manager" | "staff"
export type AttendanceStatus = "present" | "absent" | "late" | "half_day" | "on_leave"
export type TaskStatus = "todo" | "in_progress" | "completed" | "cancelled"
export type TaskPriority = "low" | "medium" | "high" | "urgent"
export type PayrollStatus = "draft" | "processed" | "paid"

export interface User {
  id: string
  email: string
  full_name: string
  role: UserRole
  department?: string
  position?: string
  phone?: string
  hire_date?: string
  salary?: number
  is_active: boolean
  email_verified: boolean
  last_login?: string
  created_at: string
  updated_at: string
}

export interface UserSession {
  id: string
  user_id: string
  session_token: string
  expires_at: string
  created_at: string
}

export interface Attendance {
  id: string
  user_id: string
  date: string
  check_in_time?: string
  check_out_time?: string
  status: AttendanceStatus
  hours_worked?: number
  notes?: string
  created_by?: string
  created_at: string
  updated_at: string
}

export interface Task {
  id: string
  title: string
  description?: string
  assigned_to?: string
  assigned_by: string
  status: TaskStatus
  priority: TaskPriority
  due_date?: string
  completed_at?: string
  created_at: string
  updated_at: string
}

export interface Payroll {
  id: string
  user_id: string
  pay_period_start: string
  pay_period_end: string
  base_salary: number
  overtime_hours: number
  overtime_rate: number
  bonuses: number
  deductions: number
  gross_pay: number
  tax_deductions: number
  net_pay: number
  status: PayrollStatus
  processed_by?: string
  processed_at?: string
  created_at: string
}

export interface Permission {
  id: string
  name: string
  description?: string
  resource: string
  action: string
  created_at: string
}

// Simple session token generator without JWT
export function generateSessionToken(): string {
  return randomBytes(32).toString("hex")
}

// Database helper functions with proper error handling
export const db = {
  // User operations
  async getUserByEmail(email: string): Promise<User | null> {
    if (typeof window !== 'undefined') {
      throw new Error("Database operations can only be performed on the server side")
    }

    try {
      if (!email) {
        throw new Error("Email is required")
      }

      const result = await sql`
        SELECT * FROM users WHERE email = ${email} AND is_active = true
      `
      return result[0] || null
    } catch (error) {
      console.error("Error fetching user by email:", error)
      throw new Error("Failed to fetch user")
    }
  },

  async getUserById(id: string): Promise<User | null> {
    if (typeof window !== 'undefined') {
      throw new Error("Database operations can only be performed on the server side")
    }

    try {
      if (!id) {
        throw new Error("User ID is required")
      }

      const result = await sql`
        SELECT * FROM users WHERE id = ${id} AND is_active = true
      `
      return result[0] || null
    } catch (error) {
      console.error("Error fetching user by ID:", error)
      throw new Error("Failed to fetch user")
    }
  },

  async createUser(
    userData: Omit<User, "id" | "created_at" | "updated_at"> & { password_hash: string },
  ): Promise<User> {
    try {
      if (!userData.email || !userData.password_hash || !userData.full_name) {
        throw new Error("Email, password hash, and full name are required")
      }

      const result = await sql`
        INSERT INTO users (
          email, password_hash, full_name, role, department, position, 
          phone, hire_date, salary, is_active, email_verified
        ) VALUES (
          ${userData.email}, ${userData.password_hash}, ${userData.full_name}, 
          ${userData.role}, ${userData.department || null}, ${userData.position || null},
          ${userData.phone || null}, ${userData.hire_date || null}, ${userData.salary || null},
          ${userData.is_active}, ${userData.email_verified}
        ) RETURNING *
      `
      return result[0]
    } catch (error) {
      console.error("Error creating user:", error)
      if (error.message?.includes("duplicate key")) {
        throw new Error("User with this email already exists")
      }
      throw new Error("Failed to create user")
    }
  },

  async updateUserLastLogin(userId: string): Promise<void> {
    try {
      if (!userId) {
        return
      }

      await sql`
        UPDATE users SET last_login = NOW() WHERE id = ${userId}
      `
    } catch (error) {
      console.error("Error updating last login:", error)
      // Don't throw here as it's not critical
    }
  },

  // Session operations
  async createSession(userId: string, sessionToken: string, expiresAt: Date): Promise<UserSession> {
    try {
      if (!userId || !sessionToken || !expiresAt) {
        throw new Error("User ID, session token, and expiration date are required")
      }

      const result = await sql`
        INSERT INTO user_sessions (user_id, session_token, expires_at)
        VALUES (${userId}, ${sessionToken}, ${expiresAt.toISOString()})
        RETURNING *
      `
      return result[0]
    } catch (error) {
      console.error("Error creating session:", error)
      throw new Error("Failed to create session")
    }
  },

  async getSessionByToken(sessionToken: string): Promise<UserSession | null> {
    try {
      if (!sessionToken) {
        return null
      }

      const result = await sql`
        SELECT * FROM user_sessions 
        WHERE session_token = ${sessionToken} AND expires_at > NOW()
      `
      return result[0] || null
    } catch (error) {
      console.error("Error fetching session:", error)
      return null
    }
  },

  async deleteSession(sessionToken: string): Promise<void> {
    try {
      if (!sessionToken) {
        return
      }

      await sql`
        DELETE FROM user_sessions WHERE session_token = ${sessionToken}
      `
    } catch (error) {
      console.error("Error deleting session:", error)
      // Don't throw as logout should still work
    }
  },

  async deleteExpiredSessions(): Promise<void> {
    try {
      await sql`
        DELETE FROM user_sessions WHERE expires_at <= NOW()
      `
    } catch (error) {
      console.error("Error deleting expired sessions:", error)
    }
  },

  // Permission operations
  async getUserPermissions(role: UserRole): Promise<Permission[]> {
    try {
      if (!role) {
        return []
      }

      const result = await sql`
        SELECT p.* FROM permissions p
        JOIN role_permissions rp ON p.id = rp.permission_id
        WHERE rp.role = ${role}
      `
      return result
    } catch (error) {
      console.error("Error fetching permissions:", error)
      return []
    }
  },

  // Dashboard stats
  async getTodayAttendance(): Promise<number> {
    try {
      const today = new Date().toISOString().split("T")[0]
      const result = await sql`
        SELECT COUNT(*) as count FROM attendance WHERE date = ${today}
      `
      return Number.parseInt(result[0]?.count || "0")
    } catch (error) {
      console.error("Error fetching attendance stats:", error)
      return 0
    }
  },

  async getTaskStats(): Promise<{ active: number; completed: number }> {
    try {
      const result = await sql`
        SELECT 
          COUNT(CASE WHEN status != 'completed' THEN 1 END) as active,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed
        FROM tasks
      `
      return {
        active: Number.parseInt(result[0]?.active || "0"),
        completed: Number.parseInt(result[0]?.completed || "0"),
      }
    } catch (error) {
      console.error("Error fetching task stats:", error)
      return { active: 0, completed: 0 }
    }
  },

  async getPendingPayrollCount(): Promise<number> {
    try {
      const result = await sql`
        SELECT COUNT(*) as count FROM payroll WHERE status = 'draft'
      `
      return Number.parseInt(result[0]?.count || "0")
    } catch (error) {
      console.error("Error fetching payroll stats:", error)
      return 0
    }
  },

  async getUserStats(): Promise<{ total: number; active: number }> {
    try {
      const result = await sql`
        SELECT 
          COUNT(*) as total,
          COUNT(CASE WHEN is_active = true THEN 1 END) as active
        FROM users
      `
      return {
        total: Number.parseInt(result[0]?.total || "0"),
        active: Number.parseInt(result[0]?.active || "0"),
      }
    } catch (error) {
      console.error("Error fetching user stats:", error)
      return { total: 0, active: 0 }
    }
  },

  // Attendance operations
  async getAttendanceByUserAndDate(userId: string, date: string): Promise<Attendance | null> {
    try {
      if (!userId || !date) {
        return null
      }

      const result = await sql`
        SELECT * FROM attendance
        WHERE user_id = ${userId} AND date = ${date}
      `
      return result[0] || null
    } catch (error) {
      console.error("Error fetching attendance:", error)
      return null
    }
  },

  async clockIn(userId: string, notes?: string, entryType: string = 'regular'): Promise<Attendance | null> {
    try {
      if (!userId) {
        throw new Error("User ID is required")
      }

      const today = new Date().toISOString().split("T")[0]
      const now = new Date()

      // Check if user has an active session (checked in but not checked out)
      const activeSession = await sql`
        SELECT * FROM attendance
        WHERE user_id = ${userId}
        AND date = ${today}
        AND check_in_time IS NOT NULL
        AND check_out_time IS NULL
        AND is_active = TRUE
        ORDER BY daily_sequence DESC
        LIMIT 1
      `

      if (activeSession.length > 0) {
        throw new Error("You must check out before checking in again")
      }

      // Check daily limits (max 5 check-ins per day)
      const todayEntries = await sql`
        SELECT COUNT(*) as count FROM attendance
        WHERE user_id = ${userId}
        AND date = ${today}
        AND check_in_time IS NOT NULL
      `

      const dailyCheckIns = Number(todayEntries[0]?.count || 0)
      if (dailyCheckIns >= 5) {
        throw new Error("Maximum 5 check-ins per day allowed")
      }

      // Get next sequence number
      const maxSequence = await sql`
        SELECT COALESCE(MAX(daily_sequence), 0) as max_seq
        FROM attendance
        WHERE user_id = ${userId} AND date = ${today}
      `
      const nextSequence = Number(maxSequence[0]?.max_seq || 0) + 1

      // Determine status based on time (assuming 9 AM is standard start time)
      const checkInHour = now.getHours()
      const checkInMinute = now.getMinutes()
      const isLate = checkInHour > 9 || (checkInHour === 9 && checkInMinute > 15)
      const status: AttendanceStatus = isLate ? "late" : "present"

      // Create new attendance record
      const result = await sql`
        INSERT INTO attendance (
          user_id, date, check_in_time, status, notes,
          entry_type, daily_sequence, is_active
        )
        VALUES (
          ${userId}, ${today}, ${now.toISOString()}, ${status}, ${notes || null},
          ${entryType}, ${nextSequence}, TRUE
        )
        RETURNING *
      `
      return result[0] || null
    } catch (error) {
      console.error("Error clocking in:", error)
      throw new Error(error instanceof Error ? error.message : "Failed to clock in")
    }
  },

  async clockOut(userId: string, notes?: string): Promise<Attendance | null> {
    try {
      if (!userId) {
        throw new Error("User ID is required")
      }

      const today = new Date().toISOString().split("T")[0]
      const now = new Date()

      // Find the active session (most recent check-in without check-out)
      const activeSession = await sql`
        SELECT * FROM attendance
        WHERE user_id = ${userId}
        AND date = ${today}
        AND check_in_time IS NOT NULL
        AND check_out_time IS NULL
        AND is_active = TRUE
        ORDER BY daily_sequence DESC
        LIMIT 1
      `

      if (activeSession.length === 0) {
        throw new Error("No active session found. Please check in first")
      }

      const session = activeSession[0]

      // Check daily limits (max 5 check-outs per day)
      const todayCheckOuts = await sql`
        SELECT COUNT(*) as count FROM attendance
        WHERE user_id = ${userId}
        AND date = ${today}
        AND check_out_time IS NOT NULL
      `

      const dailyCheckOuts = Number(todayCheckOuts[0]?.count || 0)
      if (dailyCheckOuts >= 5) {
        throw new Error("Maximum 5 check-outs per day allowed")
      }

      // Calculate hours worked for this session
      const checkInTime = new Date(session.check_in_time)
      const hoursWorked = (now.getTime() - checkInTime.getTime()) / (1000 * 60 * 60)

      // Update the active session
      const result = await sql`
        UPDATE attendance
        SET check_out_time = ${now.toISOString()},
            hours_worked = ${Math.round(hoursWorked * 100) / 100},
            notes = ${notes ? (session.notes ? `${session.notes}; ${notes}` : notes) : session.notes},
            is_active = FALSE,
            updated_at = NOW()
        WHERE id = ${session.id}
        RETURNING *
      `
      return result[0] || null
    } catch (error) {
      console.error("Error clocking out:", error)
      throw new Error(error instanceof Error ? error.message : "Failed to clock out")
    }
  },

  async getTodayAttendanceForUser(userId: string): Promise<Attendance | null> {
    try {
      const today = new Date().toISOString().split("T")[0]
      // Get the most recent attendance record for today
      const result = await sql`
        SELECT * FROM attendance
        WHERE user_id = ${userId} AND date = ${today}
        ORDER BY daily_sequence DESC
        LIMIT 1
      `
      return result[0] || null
    } catch (error) {
      console.error("Error fetching today's attendance:", error)
      return null
    }
  },

  // New function to get all today's attendance entries for a user
  async getTodayAttendanceEntriesForUser(userId: string): Promise<Attendance[]> {
    try {
      const today = new Date().toISOString().split("T")[0]
      const result = await sql`
        SELECT * FROM attendance
        WHERE user_id = ${userId} AND date = ${today}
        ORDER BY daily_sequence ASC
      `
      return result
    } catch (error) {
      console.error("Error fetching today's attendance entries:", error)
      return []
    }
  },

  // New function to get current attendance status with enhanced business logic
  async getCurrentAttendanceStatus(userId: string): Promise<{
    isCheckedIn: boolean;
    activeSession: Attendance | null;
    todayEntries: Attendance[];
    totalHoursToday: number;
    remainingCheckIns: number;
    remainingCheckOuts: number;
    dailySummary: {
      completedSessions: number;
      activeSessions: number;
      formattedTotal: string;
    };
    warnings: string[];
  }> {
    try {
      const { calculateTotalDailyHours, handleIncompleteSession, handleMidnightTransition } = await import('./attendance-utils')
      const today = new Date().toISOString().split("T")[0]

      // Get all today's entries
      const todayEntries = await this.getTodayAttendanceEntriesForUser(userId)

      // Find active session
      const activeSession = todayEntries.find(entry =>
        entry.check_in_time && !entry.check_out_time && entry.is_active
      ) || null

      // Calculate total hours using enhanced business logic
      const dailySummary = calculateTotalDailyHours(todayEntries)

      // Calculate remaining limits
      const checkInsToday = todayEntries.filter(entry => entry.check_in_time).length
      const checkOutsToday = todayEntries.filter(entry => entry.check_out_time).length

      // Check for warnings
      const warnings: string[] = []

      if (activeSession?.check_in_time) {
        const incompleteCheck = handleIncompleteSession(activeSession.check_in_time)
        if (incompleteCheck.isOvertime) {
          warnings.push(incompleteCheck.suggestedAction)
        }

        const midnightCheck = handleMidnightTransition(activeSession.check_in_time)
        if (midnightCheck.crossesMidnight && midnightCheck.warning) {
          warnings.push(midnightCheck.warning)
        }
      }

      // Check for approaching limits
      if (checkInsToday >= 4) {
        warnings.push(`You have used ${checkInsToday} of 5 daily check-ins`)
      }
      if (checkOutsToday >= 4) {
        warnings.push(`You have used ${checkOutsToday} of 5 daily check-outs`)
      }

      return {
        isCheckedIn: !!activeSession,
        activeSession,
        todayEntries,
        totalHoursToday: dailySummary.totalHours,
        remainingCheckIns: Math.max(0, 5 - checkInsToday),
        remainingCheckOuts: Math.max(0, 5 - checkOutsToday),
        dailySummary,
        warnings
      }
    } catch (error) {
      console.error("Error getting attendance status:", error)
      return {
        isCheckedIn: false,
        activeSession: null,
        todayEntries: [],
        totalHoursToday: 0,
        remainingCheckIns: 5,
        remainingCheckOuts: 5,
        dailySummary: {
          completedSessions: 0,
          activeSessions: 0,
          formattedTotal: '00:00:00'
        },
        warnings: []
      }
    }
  },

  async getUserAttendanceHistory(userId: string, limit: number = 30): Promise<Attendance[]> {
    try {
      if (!userId) {
        return []
      }

      const result = await sql`
        SELECT * FROM attendance
        WHERE user_id = ${userId}
        ORDER BY date DESC
        LIMIT ${limit}
      `
      return result
    } catch (error) {
      console.error("Error fetching attendance history:", error)
      return []
    }
  },

  async getAllAttendanceForDate(date: string): Promise<(Attendance & { user_name: string; user_email: string })[]> {
    try {
      if (!date) {
        return []
      }

      const result = await sql`
        SELECT a.*, u.full_name as user_name, u.email as user_email
        FROM attendance a
        JOIN users u ON a.user_id = u.id
        WHERE a.date = ${date}
        ORDER BY u.full_name
      `
      return result
    } catch (error) {
      console.error("Error fetching attendance for date:", error)
      return []
    }
  },

  async getAllAttendanceForToday(): Promise<(Attendance & { user_name: string; user_email: string })[]> {
    try {
      const today = new Date().toISOString().split("T")[0]
      return await this.getAllAttendanceForDate(today)
    } catch (error) {
      console.error("Error fetching today's attendance:", error)
      return []
    }
  },

  async createAttendanceRecord(data: {
    userId: string
    date: string
    checkInTime?: string
    checkOutTime?: string
    status: AttendanceStatus
    hoursWorked?: number
    notes?: string
    createdBy: string
  }): Promise<Attendance | null> {
    try {
      const { userId, date, checkInTime, checkOutTime, status, hoursWorked, notes, createdBy } = data

      if (!userId || !date || !createdBy) {
        throw new Error("User ID, date, and created by are required")
      }

      // Check if record already exists
      const existing = await this.getAttendanceByUserAndDate(userId, date)
      if (existing) {
        throw new Error("Attendance record already exists for this date")
      }

      const result = await sql`
        INSERT INTO attendance (user_id, date, check_in_time, check_out_time, status, hours_worked, notes, created_by)
        VALUES (${userId}, ${date}, ${checkInTime || null}, ${checkOutTime || null}, ${status}, ${hoursWorked || null}, ${notes || null}, ${createdBy})
        RETURNING *
      `
      return result[0] || null
    } catch (error) {
      console.error("Error creating attendance record:", error)
      throw new Error(error instanceof Error ? error.message : "Failed to create attendance record")
    }
  },

  async updateAttendanceRecord(attendanceId: string, data: {
    checkInTime?: string
    checkOutTime?: string
    status?: AttendanceStatus
    hoursWorked?: number
    notes?: string
    updatedBy: string
  }): Promise<Attendance | null> {
    try {
      const { checkInTime, checkOutTime, status, hoursWorked, notes, updatedBy } = data

      if (!attendanceId || !updatedBy) {
        throw new Error("Attendance ID and updated by are required")
      }

      // Calculate hours worked if both times are provided
      let calculatedHours = hoursWorked
      if (checkInTime && checkOutTime && !hoursWorked) {
        const checkIn = new Date(checkInTime)
        const checkOut = new Date(checkOutTime)
        calculatedHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60)
        calculatedHours = Math.round(calculatedHours * 100) / 100
      }

      const result = await sql`
        UPDATE attendance
        SET
          check_in_time = COALESCE(${checkInTime || null}, check_in_time),
          check_out_time = COALESCE(${checkOutTime || null}, check_out_time),
          status = COALESCE(${status || null}, status),
          hours_worked = COALESCE(${calculatedHours || null}, hours_worked),
          notes = COALESCE(${notes || null}, notes),
          updated_at = NOW()
        WHERE id = ${attendanceId}
        RETURNING *
      `
      return result[0] || null
    } catch (error) {
      console.error("Error updating attendance record:", error)
      throw new Error(error instanceof Error ? error.message : "Failed to update attendance record")
    }
  },

  async deleteAttendanceRecord(attendanceId: string): Promise<boolean> {
    try {
      if (!attendanceId) {
        throw new Error("Attendance ID is required")
      }

      await sql`
        DELETE FROM attendance WHERE id = ${attendanceId}
      `
      return true
    } catch (error) {
      console.error("Error deleting attendance record:", error)
      throw new Error("Failed to delete attendance record")
    }
  },

  async getAttendanceStats(startDate?: string, endDate?: string): Promise<{
    totalPresent: number
    totalAbsent: number
    totalLate: number
    totalHalfDay: number
    totalOnLeave: number
    averageHoursWorked: number
  }> {
    try {
      const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split("T")[0]
      const end = endDate || new Date().toISOString().split("T")[0]

      const result = await sql`
        SELECT
          COUNT(CASE WHEN status = 'present' THEN 1 END) as total_present,
          COUNT(CASE WHEN status = 'absent' THEN 1 END) as total_absent,
          COUNT(CASE WHEN status = 'late' THEN 1 END) as total_late,
          COUNT(CASE WHEN status = 'half_day' THEN 1 END) as total_half_day,
          COUNT(CASE WHEN status = 'on_leave' THEN 1 END) as total_on_leave,
          AVG(hours_worked) as avg_hours_worked
        FROM attendance
        WHERE date >= ${start} AND date <= ${end}
      `

      const stats = result[0] || {}
      return {
        totalPresent: Number.parseInt(stats.total_present || "0"),
        totalAbsent: Number.parseInt(stats.total_absent || "0"),
        totalLate: Number.parseInt(stats.total_late || "0"),
        totalHalfDay: Number.parseInt(stats.total_half_day || "0"),
        totalOnLeave: Number.parseInt(stats.total_on_leave || "0"),
        averageHoursWorked: Number.parseFloat(stats.avg_hours_worked || "0"),
      }
    } catch (error) {
      console.error("Error fetching attendance stats:", error)
      return {
        totalPresent: 0,
        totalAbsent: 0,
        totalLate: 0,
        totalHalfDay: 0,
        totalOnLeave: 0,
        averageHoursWorked: 0,
      }
    }
  },

  async getUserAttendanceStats(userId: string, startDate?: string, endDate?: string): Promise<{
    totalDays: number
    presentDays: number
    absentDays: number
    lateDays: number
    totalHoursWorked: number
    averageHoursPerDay: number
  }> {
    try {
      if (!userId) {
        return {
          totalDays: 0,
          presentDays: 0,
          absentDays: 0,
          lateDays: 0,
          totalHoursWorked: 0,
          averageHoursPerDay: 0,
        }
      }

      const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split("T")[0]
      const end = endDate || new Date().toISOString().split("T")[0]

      const result = await sql`
        SELECT
          COUNT(*) as total_days,
          COUNT(CASE WHEN status IN ('present', 'late') THEN 1 END) as present_days,
          COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent_days,
          COUNT(CASE WHEN status = 'late' THEN 1 END) as late_days,
          SUM(hours_worked) as total_hours_worked,
          AVG(hours_worked) as avg_hours_per_day
        FROM attendance
        WHERE user_id = ${userId} AND date >= ${start} AND date <= ${end}
      `

      const stats = result[0] || {}
      return {
        totalDays: Number.parseInt(stats.total_days || "0"),
        presentDays: Number.parseInt(stats.present_days || "0"),
        absentDays: Number.parseInt(stats.absent_days || "0"),
        lateDays: Number.parseInt(stats.late_days || "0"),
        totalHoursWorked: Number.parseFloat(stats.total_hours_worked || "0"),
        averageHoursPerDay: Number.parseFloat(stats.avg_hours_per_day || "0"),
      }
    } catch (error) {
      console.error("Error fetching user attendance stats:", error)
      return {
        totalDays: 0,
        presentDays: 0,
        absentDays: 0,
        lateDays: 0,
        totalHoursWorked: 0,
        averageHoursPerDay: 0,
      }
    }
  },

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await sql`SELECT 1`
      return true
    } catch (error) {
      console.error("Database health check failed:", error)
      return false
    }
  },
}

export { sql }
