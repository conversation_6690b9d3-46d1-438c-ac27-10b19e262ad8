import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { db } from "@/lib/neon"

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const todayAttendance = await db.getTodayAttendanceForUser(user.id)

    return NextResponse.json({
      success: true,
      attendance: todayAttendance,
      isCheckedIn: !!(todayAttendance?.check_in_time && !todayAttendance?.check_out_time),
    })
  } catch (error) {
    console.error("Attendance status API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
