import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { db } from "@/lib/neon"

export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const { userId, action, notes } = await request.json()

    if (!userId || !action) {
      return NextResponse.json({ error: "User ID and action are required" }, { status: 400 })
    }

    if (!["clock-in", "clock-out"].includes(action)) {
      return NextResponse.json({ error: "Invalid action. Must be 'clock-in' or 'clock-out'" }, { status: 400 })
    }

    let attendance
    if (action === "clock-in") {
      attendance = await db.clockIn(userId, notes)
    } else {
      attendance = await db.clockOut(userId, notes)
    }

    return NextResponse.json({
      success: true,
      attendance,
      message: `Successfully ${action.replace("-", "ed ")} employee`,
    })
  } catch (error) {
    console.error("Manual clock API error:", error)
    const message = error instanceof Error ? error.message : `Failed to ${action} employee`
    return NextResponse.json({ error: message }, { status: 400 })
  }
}
