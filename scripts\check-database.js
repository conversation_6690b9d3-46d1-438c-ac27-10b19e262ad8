const { neon } = require('@neondatabase/serverless');

async function checkDatabase() {
  try {
    // Use the connection string directly
    const DATABASE_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require";
    console.log('Using DATABASE_URL');
    const sql = neon(DATABASE_URL);
    
    console.log('🔍 Checking database connection...');
    const result = await sql`SELECT NOW() as current_time`;
    console.log('✅ Database connected successfully');
    console.log('Current time:', result[0].current_time);
    
    console.log('\n🔍 Checking attendance table structure...');
    const columns = await sql`
      SELECT 
        column_name, 
        data_type, 
        is_nullable,
        column_default
      FROM information_schema.columns 
      WHERE table_name = 'attendance' 
      ORDER BY column_name
    `;
    
    if (columns.length === 0) {
      console.log('❌ Attendance table does not exist');
      return;
    } 
    
    console.log('✅ Attendance table found with columns:');
    columns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });
    
    // Check specifically for the problematic columns
    const timeColumns = columns.filter(col => 
      ['check_in_time', 'check_out_time'].includes(col.column_name)
    );
    
    console.log('\n🔍 Time columns status:');
    timeColumns.forEach(col => {
      const isCorrect = col.data_type === 'timestamp with time zone';
      console.log(`  - ${col.column_name}: ${col.data_type} ${isCorrect ? '✅' : '❌'}`);
    });
    
    console.log('\n🔍 Checking for existing attendance records...');
    const count = await sql`SELECT COUNT(*) as count FROM attendance`;
    console.log(`Found ${count[0].count} attendance records`);
    
    // Check for unique constraint
    console.log('\n🔍 Checking table constraints...');
    const constraints = await sql`
      SELECT 
        tc.constraint_name,
        tc.constraint_type,
        kcu.column_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu 
        ON tc.constraint_name = kcu.constraint_name
      WHERE tc.table_name = 'attendance'
      AND tc.constraint_type = 'UNIQUE'
    `;
    
    if (constraints.length > 0) {
      console.log('Found UNIQUE constraints:');
      constraints.forEach(constraint => {
        console.log(`  - ${constraint.constraint_name}: ${constraint.column_name}`);
      });
    } else {
      console.log('No UNIQUE constraints found');
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

checkDatabase();
