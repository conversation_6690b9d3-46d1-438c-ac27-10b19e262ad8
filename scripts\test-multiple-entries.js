const { neon } = require('@neondatabase/serverless');

async function testMultipleEntries() {
  try {
    const DATABASE_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require";
    const sql = neon(DATABASE_URL);
    
    console.log('🧪 Testing Multiple Daily Entries System');
    console.log('========================================\n');
    
    // Get a test user
    const users = await sql`SELECT id, email FROM users WHERE role = 'staff' LIMIT 1`;
    if (users.length === 0) {
      console.log('❌ No staff user found');
      return;
    }
    
    const testUserId = users[0].id;
    const today = new Date().toISOString().split('T')[0];
    
    console.log(`Using test user: ${users[0].email} (${testUserId})`);
    
    // Clean up any existing records for today
    console.log('\n1. Cleaning up existing records for today...');
    await sql`DELETE FROM attendance WHERE user_id = ${testUserId} AND date = ${today}`;
    console.log('✅ Cleaned up');
    
    // Test multiple check-ins and check-outs
    console.log('\n2. Testing multiple check-in/check-out cycles...');
    
    const testCycles = [
      { action: 'check-in', notes: 'Morning start', expectedSequence: 1 },
      { action: 'check-out', notes: 'Coffee break', expectedSequence: 1 },
      { action: 'check-in', notes: 'Back from break', expectedSequence: 2 },
      { action: 'check-out', notes: 'Lunch break', expectedSequence: 2 },
      { action: 'check-in', notes: 'After lunch', expectedSequence: 3 }
    ];
    
    for (let i = 0; i < testCycles.length; i++) {
      const cycle = testCycles[i];
      console.log(`\n  ${i + 1}. Testing ${cycle.action}: ${cycle.notes}`);
      
      try {
        if (cycle.action === 'check-in') {
          const result = await sql`
            INSERT INTO attendance (
              user_id, date, check_in_time, status, notes, 
              entry_type, daily_sequence, is_active
            )
            VALUES (
              ${testUserId}, ${today}, NOW(), 'present', ${cycle.notes},
              'regular', ${cycle.expectedSequence}, TRUE
            )
            RETURNING id, daily_sequence, check_in_time, is_active
          `;
          console.log(`     ✅ Check-in successful: Sequence ${result[0].daily_sequence}, Active: ${result[0].is_active}`);
        } else {
          // Find active session to check out
          const activeSession = await sql`
            SELECT id, daily_sequence FROM attendance 
            WHERE user_id = ${testUserId} 
            AND date = ${today} 
            AND is_active = TRUE
            ORDER BY daily_sequence DESC
            LIMIT 1
          `;
          
          if (activeSession.length === 0) {
            console.log('     ❌ No active session found');
            continue;
          }
          
          const sessionId = activeSession[0].id;
          const checkInTime = await sql`SELECT check_in_time FROM attendance WHERE id = ${sessionId}`;
          const checkInTimestamp = new Date(checkInTime[0].check_in_time);
          const now = new Date();
          const hoursWorked = (now.getTime() - checkInTimestamp.getTime()) / (1000 * 60 * 60);
          
          const result = await sql`
            UPDATE attendance
            SET check_out_time = NOW(),
                hours_worked = ${Math.round(hoursWorked * 100) / 100},
                notes = CASE 
                  WHEN notes IS NOT NULL THEN notes || '; ' || ${cycle.notes}
                  ELSE ${cycle.notes}
                END,
                is_active = FALSE
            WHERE id = ${sessionId}
            RETURNING id, daily_sequence, check_out_time, hours_worked, is_active
          `;
          console.log(`     ✅ Check-out successful: Sequence ${result[0].daily_sequence}, Hours: ${result[0].hours_worked}, Active: ${result[0].is_active}`);
        }
        
        // Small delay between operations
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.log(`     ❌ ${cycle.action} failed:`, error.message);
      }
    }
    
    // Check final state
    console.log('\n3. Checking final attendance state...');
    const finalEntries = await sql`
      SELECT 
        daily_sequence,
        check_in_time,
        check_out_time,
        hours_worked,
        is_active,
        notes,
        entry_type
      FROM attendance 
      WHERE user_id = ${testUserId} AND date = ${today}
      ORDER BY daily_sequence ASC
    `;
    
    console.log('Final entries:');
    finalEntries.forEach(entry => {
      const checkIn = entry.check_in_time ? new Date(entry.check_in_time).toLocaleTimeString() : 'N/A';
      const checkOut = entry.check_out_time ? new Date(entry.check_out_time).toLocaleTimeString() : 'N/A';
      console.log(`  Seq ${entry.daily_sequence}: ${checkIn} → ${checkOut} (${entry.hours_worked || 0}h) [Active: ${entry.is_active}]`);
    });
    
    // Calculate total hours
    const totalHours = finalEntries.reduce((total, entry) => total + (Number(entry.hours_worked) || 0), 0);
    console.log(`\nTotal hours worked today: ${Math.round(totalHours * 100) / 100}h`);
    
    // Check active sessions
    const activeSessions = finalEntries.filter(entry => entry.is_active);
    console.log(`Active sessions: ${activeSessions.length}`);
    
    console.log('\n✅ Multiple entries system test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

testMultipleEntries();
