import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { db } from "@/lib/neon"

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager", "manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const limit = Number.parseInt(searchParams.get("limit") || "100")
    const search = searchParams.get("search") || ""

    let users
    if (search) {
      users = await db.sql`
        SELECT id, email, full_name, role, department, position, is_active
        FROM users 
        WHERE (full_name ILIKE ${`%${search}%`} OR email ILIKE ${`%${search}%`})
        AND is_active = true
        ORDER BY full_name
        LIMIT ${limit}
      `
    } else {
      users = await db.sql`
        SELECT id, email, full_name, role, department, position, is_active
        FROM users 
        WHERE is_active = true
        ORDER BY full_name
        LIMIT ${limit}
      `
    }

    return NextResponse.json({
      success: true,
      users,
    })
  } catch (error) {
    console.error("Admin users API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
