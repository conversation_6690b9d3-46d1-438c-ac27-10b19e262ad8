const { neon } = require('@neondatabase/serverless');

async function modifySchemaForMultipleEntries() {
  try {
    const DATABASE_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require";
    const sql = neon(DATABASE_URL);
    
    console.log('🔧 Modifying Database Schema for Multiple Daily Entries');
    console.log('======================================================\n');
    
    // Step 1: Check current constraints
    console.log('1. Checking current UNIQUE constraints...');
    const constraints = await sql`
      SELECT 
        tc.constraint_name,
        tc.constraint_type,
        kcu.column_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu 
        ON tc.constraint_name = kcu.constraint_name
      WHERE tc.table_name = 'attendance'
      AND tc.constraint_type = 'UNIQUE'
    `;
    
    console.log('Found constraints:');
    constraints.forEach(constraint => {
      console.log(`  - ${constraint.constraint_name}: ${constraint.column_name}`);
    });
    
    // Step 2: Remove the UNIQUE constraint
    console.log('\n2. Removing UNIQUE(user_id, date) constraint...');
    try {
      await sql`ALTER TABLE attendance DROP CONSTRAINT IF EXISTS attendance_user_id_date_key`;
      console.log('✅ UNIQUE constraint removed successfully');
    } catch (error) {
      console.log('⚠️  Constraint removal failed (might not exist):', error.message);
    }
    
    // Step 3: Add new columns for multiple entries support
    console.log('\n3. Adding new columns for multiple entries...');
    
    // Add entry_type column to distinguish between different types of entries
    try {
      await sql`
        ALTER TABLE attendance 
        ADD COLUMN IF NOT EXISTS entry_type VARCHAR(20) DEFAULT 'regular' 
        CHECK (entry_type IN ('regular', 'break', 'meeting', 'overtime'))
      `;
      console.log('✅ Added entry_type column');
    } catch (error) {
      console.log('⚠️  entry_type column might already exist:', error.message);
    }
    
    // Add sequence number for tracking multiple entries per day
    try {
      await sql`
        ALTER TABLE attendance 
        ADD COLUMN IF NOT EXISTS daily_sequence INTEGER DEFAULT 1
      `;
      console.log('✅ Added daily_sequence column');
    } catch (error) {
      console.log('⚠️  daily_sequence column might already exist:', error.message);
    }
    
    // Add is_active flag to track current work session
    try {
      await sql`
        ALTER TABLE attendance 
        ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT FALSE
      `;
      console.log('✅ Added is_active column');
    } catch (error) {
      console.log('⚠️  is_active column might already exist:', error.message);
    }
    
    // Step 4: Update existing records to have proper sequence numbers
    console.log('\n4. Updating existing records with sequence numbers...');
    await sql`
      UPDATE attendance 
      SET daily_sequence = 1, 
          entry_type = 'regular',
          is_active = CASE 
            WHEN check_in_time IS NOT NULL AND check_out_time IS NULL THEN TRUE 
            ELSE FALSE 
          END
      WHERE daily_sequence IS NULL OR daily_sequence = 0
    `;
    console.log('✅ Updated existing records');
    
    // Step 5: Create new indexes for performance
    console.log('\n5. Creating performance indexes...');
    try {
      await sql`
        CREATE INDEX IF NOT EXISTS idx_attendance_user_date_sequence 
        ON attendance(user_id, date, daily_sequence)
      `;
      console.log('✅ Created user-date-sequence index');
    } catch (error) {
      console.log('⚠️  Index creation failed:', error.message);
    }
    
    try {
      await sql`
        CREATE INDEX IF NOT EXISTS idx_attendance_active_sessions 
        ON attendance(user_id, date, is_active) 
        WHERE is_active = TRUE
      `;
      console.log('✅ Created active sessions index');
    } catch (error) {
      console.log('⚠️  Active sessions index creation failed:', error.message);
    }
    
    // Step 6: Verify the changes
    console.log('\n6. Verifying schema changes...');
    const updatedColumns = await sql`
      SELECT 
        column_name, 
        data_type, 
        is_nullable,
        column_default
      FROM information_schema.columns 
      WHERE table_name = 'attendance' 
      ORDER BY column_name
    `;
    
    console.log('Updated table structure:');
    updatedColumns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });
    
    // Check constraints again
    const newConstraints = await sql`
      SELECT 
        tc.constraint_name,
        tc.constraint_type
      FROM information_schema.table_constraints tc
      WHERE tc.table_name = 'attendance'
      AND tc.constraint_type = 'UNIQUE'
    `;
    
    console.log('\nRemaining UNIQUE constraints:');
    if (newConstraints.length === 0) {
      console.log('  ✅ No UNIQUE constraints (multiple entries now allowed)');
    } else {
      newConstraints.forEach(constraint => {
        console.log(`  - ${constraint.constraint_name}: ${constraint.constraint_type}`);
      });
    }
    
    console.log('\n✅ Schema modification completed successfully!');
    console.log('🎉 The database now supports multiple daily check-ins and check-outs');
    
  } catch (error) {
    console.error('❌ Schema modification failed:', error.message);
    console.error('Full error:', error);
  }
}

modifySchemaForMultipleEntries();
