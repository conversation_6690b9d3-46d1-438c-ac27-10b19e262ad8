// Utility functions for attendance management

export interface TimeCalculation {
  hours: number
  minutes: number
  totalHours: number
  formattedDuration: string
}

/**
 * Calculate the time difference between two timestamps
 */
export function calculateWorkingHours(checkInTime: string, checkOutTime: string): TimeCalculation {
  const checkIn = new Date(checkInTime)
  const checkOut = new Date(checkOutTime)
  
  const diffMs = checkOut.getTime() - checkIn.getTime()
  const totalMinutes = Math.floor(diffMs / (1000 * 60))
  const hours = Math.floor(totalMinutes / 60)
  const minutes = totalMinutes % 60
  const totalHours = Math.round((diffMs / (1000 * 60 * 60)) * 100) / 100

  return {
    hours,
    minutes,
    totalHours,
    formattedDuration: `${hours}h ${minutes}m`
  }
}

/**
 * Format time for display
 */
export function formatTime(timeString?: string): string {
  if (!timeString) return "-"
  return new Date(timeString).toLocaleTimeString([], { 
    hour: "2-digit", 
    minute: "2-digit",
    hour12: true 
  })
}

/**
 * Format time for input fields (24-hour format)
 */
export function formatTimeForInput(timeString: string): string {
  return new Date(timeString).toTimeString().slice(0, 5)
}

/**
 * Format date for display
 */
export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString([], {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

/**
 * Get current time in ISO format
 */
export function getCurrentISOTime(): string {
  return new Date().toISOString()
}

/**
 * Get today's date in YYYY-MM-DD format
 */
export function getTodayDate(): string {
  return new Date().toISOString().split('T')[0]
}

/**
 * Check if a time is considered late (after 9:15 AM)
 */
export function isLateCheckIn(checkInTime: string): boolean {
  const checkIn = new Date(checkInTime)
  const hour = checkIn.getHours()
  const minute = checkIn.getMinutes()
  
  // Consider late if after 9:15 AM
  return hour > 9 || (hour === 9 && minute > 15)
}

/**
 * Get attendance status based on check-in time
 */
export function getAttendanceStatus(checkInTime?: string, checkOutTime?: string): 'present' | 'late' | 'absent' {
  if (!checkInTime) return 'absent'
  if (isLateCheckIn(checkInTime)) return 'late'
  return 'present'
}

/**
 * Calculate expected work hours for a day (default 8 hours)
 */
export function getExpectedWorkHours(): number {
  return 8
}

/**
 * Check if work hours are sufficient
 */
export function isSufficientWorkHours(hoursWorked: number): boolean {
  return hoursWorked >= getExpectedWorkHours()
}

/**
 * Get time zone offset for proper time handling
 */
export function getTimezoneOffset(): number {
  return new Date().getTimezoneOffset()
}

/**
 * Convert local time to UTC for database storage
 */
export function localTimeToUTC(localTime: string, date: string): string {
  const localDateTime = new Date(`${date}T${localTime}`)
  return localDateTime.toISOString()
}

/**
 * Convert UTC time to local time for display
 */
export function utcToLocalTime(utcTime: string): string {
  return new Date(utcTime).toLocaleTimeString([], { 
    hour: "2-digit", 
    minute: "2-digit" 
  })
}

/**
 * Get work duration in real-time for currently checked-in users
 */
export function getCurrentWorkDuration(checkInTime: string): TimeCalculation {
  return calculateWorkingHours(checkInTime, getCurrentISOTime())
}

/**
 * Validate time input format
 */
export function isValidTimeFormat(time: string): boolean {
  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
  return timeRegex.test(time)
}

/**
 * Get attendance statistics summary
 */
export function getAttendanceStatsSummary(stats: {
  totalDays: number
  presentDays: number
  absentDays: number
  lateDays: number
  totalHoursWorked: number
}) {
  const attendanceRate = stats.totalDays > 0 ? (stats.presentDays / stats.totalDays) * 100 : 0
  const punctualityRate = stats.presentDays > 0 ? ((stats.presentDays - stats.lateDays) / stats.presentDays) * 100 : 0
  const avgHoursPerDay = stats.totalDays > 0 ? stats.totalHoursWorked / stats.totalDays : 0

  return {
    attendanceRate: Math.round(attendanceRate * 100) / 100,
    punctualityRate: Math.round(punctualityRate * 100) / 100,
    avgHoursPerDay: Math.round(avgHoursPerDay * 100) / 100
  }
}
